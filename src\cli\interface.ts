import * as readline from 'readline';
import chalk from 'chalk';
import ora from 'ora';
import { EventEmitter } from 'events';
import * as path from 'path';
import { Message, ChatSession, ExecutionPlan, CLIConfig } from '../types';
import { ContextManager } from '../context/manager';
import { ModelManager } from '../models/manager';
import { ToolManager } from '../tools/manager';
import { PlanExecutor } from '../core/executor';
import { ErrorDetector } from '../core/error-detector';
import { DiffReviewer } from '../core/diff-reviewer';

export class CLIInterface extends EventEmitter {
  private rl: readline.Interface;
  private contextManager: ContextManager;
  private modelManager: ModelManager;
  private toolManager: ToolManager;
  private planExecutor: PlanExecutor;
  private errorDetector: ErrorDetector;
  private diffReviewer: DiffReviewer;
  private currentSession: ChatSession | null = null;
  private config: CLIConfig;
  private spinner: any;
  private isProcessing: boolean = false;
  private escapeCount: number = 0;
  private escapeTimer: NodeJS.Timeout | null = null;
  private streamingResponse: boolean = false;

  constructor(config: CLIConfig) {
    super();
    this.config = config;
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
      prompt: chalk.cyan('kritrima> ')
    });

    this.contextManager = new ContextManager(config.contextSize);
    this.modelManager = new ModelManager();
    this.toolManager = new ToolManager();
    this.planExecutor = new PlanExecutor(this.toolManager);
    this.errorDetector = new ErrorDetector(config.workingDirectory);
    this.diffReviewer = new DiffReviewer();
    this.spinner = ora();

    this.setupEventHandlers();
    this.initializeSession();
  }

  private setupEventHandlers(): void {
    this.rl.on('line', this.handleUserInput.bind(this));
    this.rl.on('close', this.handleExit.bind(this));

    // Handle key presses for double ESC functionality
    process.stdin.on('keypress', this.handleKeyPress.bind(this));
    if (process.stdin.isTTY) {
      process.stdin.setRawMode(true);
    }

    this.planExecutor.on('stepStart', this.handleStepStart.bind(this));
    this.planExecutor.on('stepComplete', this.handleStepComplete.bind(this));
    this.planExecutor.on('planComplete', this.handlePlanComplete.bind(this));

    this.errorDetector.on('errorDetected', this.handleErrorDetected.bind(this));
  }

  private async initializeSession(): Promise<void> {
    try {
      this.currentSession = {
        id: this.generateSessionId(),
        messages: [],
        context: [],
        model: await this.modelManager.getDefaultModel(),
        config: await this.modelManager.getDefaultConfig(),
        createdAt: new Date(),
        updatedAt: new Date()
      };
    } catch (error) {
      // No models available - create a minimal session
      console.log(chalk.yellow('⚠️  No AI models configured. Some features will be limited.'));
      console.log(chalk.gray('   Use the "models" command to see available models.'));
      console.log(chalk.gray('   Use the "model <name>" command to switch models.'));

      this.currentSession = {
        id: this.generateSessionId(),
        messages: [],
        context: [],
        model: { name: 'none', provider: 'none', maxTokens: 0, supportsStreaming: false, supportsFunctionCalling: false } as any,
        config: { model: 'none', temperature: 0.7, maxTokens: 4096 } as any,
        createdAt: new Date(),
        updatedAt: new Date()
      };
    }

    // Load initial context from working directory with progress indication
    try {
      await this.loadContextWithProgress(this.config.workingDirectory);
    } catch (error) {
      console.log(chalk.yellow('⚠️  Failed to load initial context. You can add files manually using "context add <path>".'));
    }

    // Start real-time codebase monitoring
    this.startCodebaseMonitoring();
  }

  public start(): void {
    this.displayWelcome();
    // Wait a moment for session initialization to complete
    setTimeout(() => {
      this.displayHeader();
      this.displayStatus();
      this.rl.prompt();
    }, 100);
  }

  private displayWelcome(): void {
    console.log(chalk.cyan('\n🤖 Welcome to Kritrima AI - Your Intelligent Coding Assistant\n'));
    console.log(chalk.gray('Type "help" for available commands or start asking questions!\n'));
  }

  private displayStatus(): void {
    if (!this.currentSession) {
      console.log(chalk.yellow('⚠️  Session initializing...'));
      return;
    }

    const contextFiles = this.currentSession.context.length || 0;
    const totalTokens = this.contextManager.getTotalTokens();
    const model = this.currentSession.model.name || 'loading';
    const provider = this.currentSession.model.provider || 'unknown';
    const sessionAge = Math.round((Date.now() - this.currentSession.createdAt.getTime()) / 1000 / 60);

    console.log(chalk.blue('📊 Current Status:'));
    console.log(chalk.white(`   • Context: ${contextFiles} files (${totalTokens.toLocaleString()} tokens)`));
    console.log(chalk.white(`   • Model: ${model} (${provider})`));
    console.log(chalk.white(`   • Session: ${sessionAge} minutes old`));
    console.log(chalk.white(`   • Working Directory: ${path.basename(this.config.workingDirectory)}`));
    console.log(chalk.white(`   • Autonomy Level: ${this.config.autonomyLevel}`));

    // Show context loading status
    if (contextFiles > 0) {
      console.log(chalk.green(`   • Context Status: ✅ Loaded and monitoring`));
    } else {
      console.log(chalk.yellow(`   • Context Status: ⚠️  Loading...`));
    }
    console.log();
  }

  private async handleUserInput(input: string): Promise<void> {
    const trimmedInput = input.trim();

    if (!trimmedInput) {
      this.rl.prompt();
      return;
    }

    // Handle special commands
    if (await this.handleCommand(trimmedInput)) {
      this.rl.prompt();
      return;
    }

    // Process as AI query
    await this.processAIQuery(trimmedInput);
    this.rl.prompt();
  }

  private async handleCommand(input: string): Promise<boolean> {
    const [command, ...args] = input.split(' ');

    switch (command.toLowerCase()) {
      case 'help':
        this.displayHelp();
        return true;

      case 'status':
        this.displayStatus();
        return true;

      case 'models':
        await this.displayModels();
        return true;

      case 'model':
        if (args.length > 0) {
          await this.switchModel(args[0]);
        } else {
          console.log(chalk.yellow('Please specify a model name. Use "models" to see available options.'));
        }
        return true;

      case 'context':
        await this.handleContextCommand(args);
        return true;

      case 'scan':
        await this.scanForErrors();
        return true;

      case 'clear':
        this.clearSession();
        return true;

      case 'refresh':
        this.refreshDisplay();
        this.displayStatus();
        return true;

      case 'exit':
      case 'quit':
        this.handleExit();
        return true;

      default:
        return false;
    }
  }

  private displayHelp(): void {
    console.log(chalk.cyan('\n📖 Available Commands:\n'));
    console.log(chalk.white('  help                 - Show this help message'));
    console.log(chalk.white('  status               - Show current status and context'));
    console.log(chalk.white('  models               - List available AI models'));
    console.log(chalk.white('  model <name>         - Switch to a different model'));
    console.log(chalk.white('  context add <path>   - Add file/directory to context'));
    console.log(chalk.white('  context remove <path> - Remove file from context'));
    console.log(chalk.white('  context list         - List files in context'));
    console.log(chalk.white('  scan                 - Scan for errors in working directory'));
    console.log(chalk.white('  clear                - Clear current session'));
    console.log(chalk.white('  refresh              - Refresh display and status'));
    console.log(chalk.white('  exit/quit            - Exit the application'));
    console.log(chalk.cyan('\n🔧 Special Features:\n'));
    console.log(chalk.white('  • Double ESC         - Interrupt ongoing operations'));
    console.log(chalk.white('  • Real-time Context  - Automatic file monitoring'));
    console.log(chalk.white('  • Streaming Responses- Real-time AI responses'));
    console.log(chalk.white('  • Error Auto-fix     - Automatic error detection & fixing'));
    console.log(chalk.white('  • Function Calling   - Autonomous tool execution\n'));
  }

  private async processAIQuery(query: string): Promise<void> {
    if (!this.currentSession) {
      console.log(chalk.red('❌ No active session. Please restart the application.'));
      return;
    }

    // Check if AI models are available
    if (this.currentSession.model.name === 'none') {
      console.log(chalk.yellow('⚠️  No AI model configured.'));
      console.log(chalk.gray('   Please configure a model first using:'));
      console.log(chalk.gray('   1. Run "models" to see available models'));
      console.log(chalk.gray('   2. Run "model <name>" to select a model'));
      return;
    }

    try {
      this.isProcessing = true;
      this.spinner.start('Processing query...');

      // Add user message to session
      const userMessage: Message = {
        role: 'user',
        content: query
      };
      this.currentSession.messages.push(userMessage);

      // Get AI response with function calling
      const response = await this.modelManager.generateResponse(
        this.currentSession.messages,
        this.currentSession.context,
        this.toolManager.getAvailableTools(),
        this.currentSession.config
      );

      this.spinner.stop();

      // Handle streaming response or tool calls
      if (response.toolCalls && response.toolCalls.length > 0) {
        await this.handleToolCalls(response.toolCalls);
      } else if (response.stream) {
        await this.handleStreamingResponse(response);
      } else {
        console.log(chalk.blue('\n🤖 ') + response.content);
      }

      // Add assistant message to session
      const assistantMessage: Message = {
        role: 'assistant',
        content: response.content,
        tool_calls: response.toolCalls
      };
      this.currentSession.messages.push(assistantMessage);
      this.currentSession.updatedAt = new Date();

    } catch (error) {
      this.spinner.stop();
      await this.handleErrorWithResilience(error, 'AI query processing');
    } finally {
      this.isProcessing = false;
    }
  }

  private async handleToolCalls(toolCalls: any[]): Promise<void> {
    console.log(chalk.yellow('\n🔧 Executing tools...\n'));

    for (const toolCall of toolCalls) {
      try {
        const tool = this.toolManager.getTool(toolCall.function.name);
        if (!tool) {
          console.log(chalk.red(`❌ Unknown tool: ${toolCall.function.name}`));
          continue;
        }

        console.log(chalk.blue(`🔧 Running ${toolCall.function.name}...`));

        const args = JSON.parse(toolCall.function.arguments);
        const result = await tool.execute(args);

        if (result.success) {
          console.log(chalk.green(`✅ ${toolCall.function.name} completed successfully`));
        } else {
          console.log(chalk.red(`❌ ${toolCall.function.name} failed: ${result.error}`));
        }

        // Add tool result to session
        const toolMessage: Message = {
          role: 'function',
          name: toolCall.function.name,
          content: JSON.stringify(result)
        };
        this.currentSession?.messages.push(toolMessage);

      } catch (error) {
        console.log(chalk.red(`❌ Error executing ${toolCall.function.name}: ${error}`));
      }
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private handleExit(): void {
    console.log(chalk.cyan('\n👋 Goodbye! Thanks for using Kritrima AI.\n'));
    process.exit(0);
  }

  private async displayModels(): Promise<void> {
    const models = await this.modelManager.getAvailableModels();
    console.log(chalk.cyan('\n🤖 Available Models:\n'));

    for (const model of models) {
      const current = model.name === this.currentSession?.model.name ? chalk.green('(current)') : '';
      console.log(chalk.white(`  ${model.name} - ${model.provider} ${current}`));
    }
    console.log();
  }

  private async switchModel(modelName: string): Promise<void> {
    try {
      const model = await this.modelManager.getModel(modelName);
      if (this.currentSession) {
        this.currentSession.model = model;
        this.currentSession.config = await this.modelManager.getConfigForModel(modelName);
        console.log(chalk.green(`✅ Switched to model: ${modelName}`));

        // Refresh the header to show new model
        this.refreshDisplay();
      }
    } catch (error) {
      console.log(chalk.red(`❌ Failed to switch model: ${error}`));
    }
  }

  private async handleContextCommand(args: string[]): Promise<void> {
    if (args.length === 0) {
      console.log(chalk.yellow('Please specify a context action: add, remove, or list'));
      return;
    }

    const [action, path] = args;

    switch (action.toLowerCase()) {
      case 'add':
        if (path) {
          await this.addToContext(path);
        } else {
          console.log(chalk.yellow('Please specify a file or directory path to add'));
        }
        break;

      case 'remove':
        if (path) {
          this.removeFromContext(path);
        } else {
          console.log(chalk.yellow('Please specify a file path to remove'));
        }
        break;

      case 'list':
        this.listContext();
        break;

      default:
        console.log(chalk.yellow('Unknown context action. Use: add, remove, or list'));
    }
  }

  private async addToContext(path: string): Promise<void> {
    try {
      await this.contextManager.addFile(path);
      if (this.currentSession) {
        this.currentSession.context = this.contextManager.getContext();
      }
      console.log(chalk.green(`✅ Added ${path} to context`));
    } catch (error) {
      console.log(chalk.red(`❌ Failed to add ${path} to context: ${error}`));
    }
  }

  private removeFromContext(path: string): void {
    this.contextManager.removeFile(path);
    if (this.currentSession) {
      this.currentSession.context = this.contextManager.getContext();
    }
    console.log(chalk.green(`✅ Removed ${path} from context`));
  }

  private listContext(): void {
    const context = this.contextManager.getContext();
    console.log(chalk.cyan('\n📁 Context Files:\n'));

    if (context.length === 0) {
      console.log(chalk.gray('  No files in context'));
    } else {
      for (const file of context) {
        console.log(chalk.white(`  ${file.path} (${file.size} bytes)`));
      }
    }
    console.log();
  }

  private async scanForErrors(): Promise<void> {
    this.spinner.start('Scanning for errors...');

    try {
      const errors = await this.errorDetector.scanDirectory(this.config.workingDirectory);
      this.spinner.stop();

      if (errors.length === 0) {
        console.log(chalk.green('✅ No errors found!'));
      } else {
        console.log(chalk.yellow(`⚠️  Found ${errors.length} errors:`));
        for (const error of errors) {
          const severity = error.severity === 'error' ? chalk.red('ERROR') :
                          error.severity === 'warning' ? chalk.yellow('WARN') :
                          chalk.blue('INFO');
          console.log(`  ${severity}: ${error.file}:${error.line} - ${error.message}`);
        }

        if (this.config.autoFix) {
          console.log(chalk.blue('\n🔧 Attempting to auto-fix errors...'));
          await this.autoFixErrors(errors);
        }
      }
    } catch (error) {
      this.spinner.stop();
      console.log(chalk.red(`❌ Error scanning directory: ${error}`));
    }
  }

  private async autoFixErrors(errors: any[]): Promise<void> {
    const fixableErrors = errors.filter(e => e.fixable);

    if (fixableErrors.length === 0) {
      console.log(chalk.yellow('No auto-fixable errors found'));
      return;
    }

    for (const error of fixableErrors) {
      try {
        await this.errorDetector.fixError(error);
        console.log(chalk.green(`✅ Fixed: ${error.file}:${error.line}`));
      } catch (fixError) {
        console.log(chalk.red(`❌ Failed to fix: ${error.file}:${error.line} - ${fixError}`));
      }
    }
  }

  private clearSession(): void {
    if (this.currentSession) {
      this.currentSession.messages = [];
      console.log(chalk.green('✅ Session cleared'));
    }
  }

  private handleStepStart(step: any): void {
    console.log(chalk.blue(`🔄 Starting step: ${step.name}`));
  }

  private handleStepComplete(step: any): void {
    if (step.result?.success) {
      console.log(chalk.green(`✅ Step completed: ${step.name}`));
    } else {
      console.log(chalk.red(`❌ Step failed: ${step.name} - ${step.result?.error}`));
    }
  }

  private handlePlanComplete(plan: ExecutionPlan): void {
    const completed = plan.steps.filter(s => s.status === 'completed').length;
    const total = plan.steps.length;
    console.log(chalk.cyan(`📋 Plan completed: ${completed}/${total} steps successful`));
  }

  private handleErrorDetected(error: any): void {
    console.log(chalk.red(`🚨 Error detected: ${error.message}`));
  }

  // Enhanced UI/UX Methods
  private displayHeader(): void {
    if (!this.currentSession) {
      return;
    }

    const sessionId = this.currentSession.id.split('_')[1] || 'initializing';
    const modelName = this.currentSession.model.name || 'loading';
    const provider = this.currentSession.model.provider || 'unknown';
    const workingDir = path.basename(this.config.workingDirectory);

    // Clear previous header and display new one
    console.log(chalk.bgBlue.white.bold(' KRITRIMA AI ') +
                chalk.bgGray.white(` Session: ${sessionId} `) +
                chalk.bgGreen.white(` Model: ${modelName} (${provider}) `) +
                chalk.bgYellow.black(` Directory: ${workingDir} `));
    console.log();
  }

  private refreshDisplay(): void {
    console.log(); // Add some space
    this.displayHeader();
  }

  private handleKeyPress(_str: string, key: any): void {
    if (key && key.name === 'escape') {
      this.escapeCount++;

      if (this.escapeTimer) {
        clearTimeout(this.escapeTimer);
      }

      this.escapeTimer = setTimeout(() => {
        this.escapeCount = 0;
      }, 500); // Reset after 500ms

      if (this.escapeCount === 2) {
        this.handleDoubleEscape();
        this.escapeCount = 0;
      }
    }
  }

  private handleDoubleEscape(): void {
    if (this.isProcessing || this.streamingResponse) {
      console.log(chalk.yellow('\n⚠️  Interrupting current operation...'));
      this.isProcessing = false;
      this.streamingResponse = false;

      if (this.spinner.isSpinning) {
        this.spinner.stop();
      }

      console.log(chalk.cyan('✅ Operation interrupted. You can continue with a new query.'));
      this.rl.prompt();
    } else {
      console.log(chalk.blue('\n💡 Double ESC detected. Use this to interrupt ongoing operations.'));
      this.rl.prompt();
    }
  }

  // Enhanced streaming response handling
  private async handleStreamingResponse(response: any): Promise<void> {
    this.streamingResponse = true;

    try {
      if (response.stream) {
        process.stdout.write(chalk.blue('\n🤖 '));

        for await (const chunk of response.stream) {
          if (!this.streamingResponse) {
            break; // Interrupted by double ESC
          }

          if (chunk.content) {
            process.stdout.write(chunk.content);
          }
        }

        console.log(); // New line after streaming
      } else {
        console.log(chalk.blue('\n🤖 ') + response.content);
      }
    } catch (error) {
      console.log(chalk.red(`\n❌ Streaming error: ${error}`));
    } finally {
      this.streamingResponse = false;
    }
  }

  // Enhanced error handling with resilience
  private async handleErrorWithResilience(error: any, context: string): Promise<void> {
    console.log(chalk.red(`❌ Error in ${context}: ${error.message}`));

    // Log detailed error for debugging
    if (this.config.logLevel === 'debug') {
      console.log(chalk.gray('Debug info:'), error.stack);
    }

    // Attempt recovery based on error type
    if (error.message.includes('API key')) {
      console.log(chalk.yellow('💡 Tip: Check your API keys in the .env file'));
    } else if (error.message.includes('network') || error.message.includes('timeout')) {
      console.log(chalk.yellow('💡 Tip: Check your internet connection and try again'));
    } else if (error.message.includes('model')) {
      console.log(chalk.yellow('💡 Tip: Try switching to a different model with "model <name>"'));
    }

    // Graceful degradation
    console.log(chalk.cyan('🔄 Kritrima AI is still running. You can continue with other operations.'));
  }

  // Enhanced context loading with progress indication
  private async loadContextWithProgress(directory: string): Promise<void> {
    const spinner = ora('Loading codebase context...').start();

    try {
      await this.contextManager.loadWorkingDirectory(directory);
      const contextFiles = this.contextManager.getContext();
      const totalTokens = this.contextManager.getTotalTokens();

      spinner.succeed(`Loaded ${contextFiles.length} files (${totalTokens.toLocaleString()} tokens)`);

      if (this.currentSession) {
        this.currentSession.context = contextFiles;
      }

      // Update display after context is loaded
      setTimeout(() => {
        this.refreshDisplay();
      }, 50);

    } catch (error) {
      spinner.fail(`Failed to load context: ${error}`);
      throw error;
    }
  }

  // Real-time codebase monitoring
  private startCodebaseMonitoring(): void {
    const chokidar = require('chokidar');

    const watcher = chokidar.watch(this.config.workingDirectory, {
      ignored: /(^|[\/\\])\../, // ignore dotfiles
      persistent: true,
      ignoreInitial: true
    });

    watcher.on('change', async (filePath: string) => {
      if (this.shouldMonitorFile(filePath)) {
        console.log(chalk.gray(`📝 File changed: ${path.basename(filePath)}`));

        try {
          await this.contextManager.addFile(filePath);
          if (this.currentSession) {
            this.currentSession.context = this.contextManager.getContext();
          }
        } catch (error) {
          // Silent fail for file monitoring
        }
      }
    });
  }

  private shouldMonitorFile(filePath: string): boolean {
    const ext = path.extname(filePath);
    const monitoredExtensions = ['.ts', '.js', '.py', '.java', '.cpp', '.c', '.go', '.rs', '.php'];
    return monitoredExtensions.includes(ext);
  }

  // Enhanced theme support
  private applyTheme(themeName: string = 'default'): void {
    const themes = {
      default: {
        primary: chalk.cyan,
        secondary: chalk.blue,
        success: chalk.green,
        warning: chalk.yellow,
        error: chalk.red,
        info: chalk.gray
      },
      dark: {
        primary: chalk.white,
        secondary: chalk.gray,
        success: chalk.green,
        warning: chalk.yellow,
        error: chalk.red,
        info: chalk.dim
      },
      matrix: {
        primary: chalk.green,
        secondary: chalk.greenBright,
        success: chalk.green,
        warning: chalk.yellow,
        error: chalk.red,
        info: chalk.dim.green
      }
    };

    // Apply theme (this would be expanded for full theme support)
    const theme = themes[themeName as keyof typeof themes] || themes.default;
    // Store theme for use throughout the interface
  }
}
