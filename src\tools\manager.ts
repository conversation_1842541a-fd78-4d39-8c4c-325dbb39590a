import { Tool } from '../types';
import { ShellTool } from './shell';
import { FileTool } from './file';
import { EditTool } from './edit';
import { WriteTool } from './write';
import { GrepTool } from './grep';
import { WebTool } from './web';

export class ToolManager {
  private tools: Map<string, Tool> = new Map();

  constructor() {
    this.initializeTools();
  }

  private initializeTools(): void {
    const tools = [
      new ShellTool(),
      new FileTool(),
      new EditTool(),
      new WriteTool(),
      new GrepTool(),
      new WebTool()
    ];

    for (const tool of tools) {
      this.tools.set(tool.name, tool);
    }
  }

  getTool(name: string): Tool | undefined {
    return this.tools.get(name);
  }

  getAvailableTools(): Tool[] {
    return Array.from(this.tools.values());
  }

  async executeTool(name: string, args: any): Promise<any> {
    const tool = this.tools.get(name);
    if (!tool) {
      throw new Error(`Tool not found: ${name}`);
    }

    return await tool.execute(args);
  }

  registerTool(tool: Tool): void {
    this.tools.set(tool.name, tool);
  }

  unregisterTool(name: string): void {
    this.tools.delete(name);
  }

  listTools(): string[] {
    return Array.from(this.tools.keys());
  }

  getToolDescription(name: string): string | undefined {
    const tool = this.tools.get(name);
    return tool?.description;
  }

  getToolParameters(name: string): any {
    const tool = this.tools.get(name);
    return tool?.parameters;
  }
}
