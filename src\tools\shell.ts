import { exec, spawn } from 'child_process';
import { promisify } from 'util';
import { Tool, ToolResult } from '../types';

const execAsync = promisify(exec);

export class ShellTool implements Tool {
  name = 'shell';
  description = 'Execute shell commands in the local environment. Can run any bash/cmd command and return the output.';
  parameters = {
    type: 'object' as const,
    properties: {
      command: {
        type: 'string',
        description: 'The shell command to execute'
      },
      cwd: {
        type: 'string',
        description: 'Working directory for the command (optional)'
      },
      timeout: {
        type: 'number',
        description: 'Timeout in milliseconds (default: 30000)'
      },
      env: {
        type: 'object',
        description: 'Environment variables to set (optional)'
      }
    },
    required: ['command']
  };

  async execute(args: {
    command: string;
    cwd?: string;
    timeout?: number;
    env?: Record<string, string>;
  }): Promise<ToolResult> {
    try {
      const { command, cwd, timeout = 30000, env } = args;

      // Security check - prevent dangerous commands
      if (this.isDangerousCommand(command)) {
        return {
          success: false,
          output: '',
          error: 'Command blocked for security reasons'
        };
      }

      const options: any = {
        timeout,
        maxBuffer: 1024 * 1024 * 10, // 10MB buffer
        encoding: 'utf8'
      };

      if (cwd) {
        options.cwd = cwd;
      }

      if (env) {
        options.env = { ...process.env, ...env };
      }

      const { stdout, stderr } = await execAsync(command, options);

      return {
        success: true,
        output: String(stdout || stderr || 'Command executed successfully'),
        metadata: {
          command,
          cwd: cwd || process.cwd(),
          exitCode: 0
        }
      };
    } catch (error: any) {
      return {
        success: false,
        output: error.stdout || '',
        error: error.stderr || error.message || 'Command execution failed',
        metadata: {
          command: args.command,
          cwd: args.cwd || process.cwd(),
          exitCode: error.code || 1
        }
      };
    }
  }

  async executeInteractive(args: {
    command: string;
    cwd?: string;
    env?: Record<string, string>;
    onOutput?: (data: string) => void;
    onError?: (data: string) => void;
  }): Promise<ToolResult> {
    return new Promise((resolve) => {
      const { command, cwd, env, onOutput, onError } = args;

      if (this.isDangerousCommand(command)) {
        resolve({
          success: false,
          output: '',
          error: 'Command blocked for security reasons'
        });
        return;
      }

      const [cmd, ...cmdArgs] = command.split(' ');
      const options: any = {};

      if (cwd) {
        options.cwd = cwd;
      }

      if (env) {
        options.env = { ...process.env, ...env };
      }

      const child = spawn(cmd, cmdArgs, options);
      let stdout = '';
      let stderr = '';

      child.stdout?.on('data', (data: Buffer) => {
        const output = data.toString();
        stdout += output;
        onOutput?.(output);
      });

      child.stderr?.on('data', (data: Buffer) => {
        const error = data.toString();
        stderr += error;
        onError?.(error);
      });

      child.on('close', (code) => {
        resolve({
          success: code === 0,
          output: stdout,
          error: stderr,
          metadata: {
            command,
            cwd: cwd || process.cwd(),
            exitCode: code || 0
          }
        });
      });

      child.on('error', (error) => {
        resolve({
          success: false,
          output: stdout,
          error: error.message,
          metadata: {
            command,
            cwd: cwd || process.cwd(),
            exitCode: 1
          }
        });
      });
    });
  }

  private isDangerousCommand(command: string): boolean {
    const dangerousPatterns = [
      /rm\s+-rf\s+\//, // rm -rf /
      /rm\s+-rf\s+\*/, // rm -rf *
      /format\s+c:/, // format c:
      /del\s+\/s\s+\/q\s+c:\\/, // del /s /q c:\
      /shutdown/, // shutdown commands
      /reboot/, // reboot commands
      /halt/, // halt commands
      /mkfs/, // filesystem formatting
      /dd\s+if=.*of=\/dev/, // disk writing
      /:(){ :|:& };:/, // fork bomb
      /curl.*\|\s*sh/, // curl | sh
      /wget.*\|\s*sh/, // wget | sh
      /chmod\s+777\s+\//, // chmod 777 /
      /chown.*\//, // chown on root
      /sudo\s+rm/, // sudo rm
      /sudo\s+dd/, // sudo dd
    ];

    return dangerousPatterns.some(pattern => pattern.test(command.toLowerCase()));
  }

  async getSystemInfo(): Promise<ToolResult> {
    const commands = process.platform === 'win32'
      ? ['systeminfo', 'wmic os get caption,version']
      : ['uname -a', 'lsb_release -a || cat /etc/os-release'];

    let output = '';
    for (const cmd of commands) {
      try {
        const result = await this.execute({ command: cmd });
        if (result.success) {
          output += `${cmd}:\n${result.output}\n\n`;
        }
      } catch (error) {
        // Continue with next command
      }
    }

    return {
      success: true,
      output: output || 'System information not available',
      metadata: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version
      }
    };
  }

  async listProcesses(): Promise<ToolResult> {
    const command = process.platform === 'win32' ? 'tasklist' : 'ps aux';
    return await this.execute({ command });
  }

  async getCurrentDirectory(): Promise<ToolResult> {
    const command = process.platform === 'win32' ? 'cd' : 'pwd';
    return await this.execute({ command });
  }

  async listFiles(directory?: string): Promise<ToolResult> {
    const dir = directory || '.';
    const command = process.platform === 'win32'
      ? `dir "${dir}"`
      : `ls -la "${dir}"`;

    return await this.execute({ command, cwd: directory });
  }
}
