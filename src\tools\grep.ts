import * as fs from 'fs';
import * as path from 'path';
import { glob } from 'glob';
import { Tool, ToolResult } from '../types';

export class GrepTool implements Tool {
  name = 'grep';
  description = 'Search for patterns in files and directories. Supports regex patterns, case-insensitive search, and recursive directory search.';
  parameters = {
    type: 'object' as const,
    properties: {
      pattern: {
        type: 'string',
        description: 'The search pattern (can be regex)'
      },
      path: {
        type: 'string',
        description: 'File or directory path to search in',
        default: '.'
      },
      recursive: {
        type: 'boolean',
        description: 'Search recursively in directories',
        default: true
      },
      case_sensitive: {
        type: 'boolean',
        description: 'Case sensitive search',
        default: false
      },
      regex: {
        type: 'boolean',
        description: 'Treat pattern as regular expression',
        default: false
      },
      include_line_numbers: {
        type: 'boolean',
        description: 'Include line numbers in results',
        default: true
      },
      context_lines: {
        type: 'number',
        description: 'Number of context lines to show around matches',
        default: 0
      },
      file_pattern: {
        type: 'string',
        description: 'File pattern to include (e.g., "*.js", "*.py")',
        default: '*'
      },
      exclude_dirs: {
        type: 'array',
        items: { type: 'string' },
        description: 'Directories to exclude from search',
        default: ['node_modules', '.git', 'dist', 'build']
      },
      max_results: {
        type: 'number',
        description: 'Maximum number of results to return',
        default: 100
      }
    },
    required: ['pattern']
  };

  async execute(args: {
    pattern: string;
    path?: string;
    recursive?: boolean;
    case_sensitive?: boolean;
    regex?: boolean;
    include_line_numbers?: boolean;
    context_lines?: number;
    file_pattern?: string;
    exclude_dirs?: string[];
    max_results?: number;
  }): Promise<ToolResult> {
    try {
      const {
        pattern,
        path: searchPath = '.',
        recursive = true,
        case_sensitive = false,
        regex = false,
        include_line_numbers = true,
        context_lines = 0,
        file_pattern = '*',
        exclude_dirs = ['node_modules', '.git', 'dist', 'build'],
        max_results = 100
      } = args;

      // Validate pattern
      if (!pattern || pattern.trim() === '') {
        throw new Error('Search pattern cannot be empty');
      }

      // Check if path exists
      if (!fs.existsSync(searchPath)) {
        throw new Error(`Path does not exist: ${searchPath}`);
      }

      const stats = fs.statSync(searchPath);
      let filesToSearch: string[] = [];

      if (stats.isFile()) {
        filesToSearch = [searchPath];
      } else if (stats.isDirectory()) {
        filesToSearch = await this.getFilesToSearch(searchPath, file_pattern, exclude_dirs, recursive);
      }

      const searchResults = await this.searchInFiles(
        filesToSearch,
        pattern,
        {
          case_sensitive,
          regex,
          include_line_numbers,
          context_lines,
          max_results
        }
      );

      return {
        success: true,
        output: this.formatResults(searchResults),
        metadata: {
          pattern,
          searchPath,
          filesSearched: filesToSearch.length,
          totalMatches: searchResults.reduce((sum, result) => sum + result.matches.length, 0),
          matchingFiles: searchResults.length
        }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: error.message || 'Search failed'
      };
    }
  }

  private async getFilesToSearch(
    dirPath: string,
    filePattern: string,
    excludeDirs: string[],
    recursive: boolean
  ): Promise<string[]> {
    const globPattern = recursive ? `**/${filePattern}` : filePattern;
    const ignorePatterns = excludeDirs.map(dir => `**/${dir}/**`);

    try {
      const files = await glob(globPattern, {
        cwd: dirPath,
        ignore: ignorePatterns,
        absolute: true,
        nodir: true
      });

      // Filter out binary files
      const textFiles: string[] = [];
      for (const file of files) {
        if (await this.isTextFile(file)) {
          textFiles.push(file);
        }
      }

      return textFiles;
    } catch (error) {
      throw new Error(`Failed to get files to search: ${error}`);
    }
  }

  private async isTextFile(filePath: string): Promise<boolean> {
    try {
      const buffer = fs.readFileSync(filePath);

      // Check for null bytes (binary indicator)
      for (let i = 0; i < Math.min(buffer.length, 1024); i++) {
        if (buffer[i] === 0) {
          return false;
        }
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  private async searchInFiles(
    files: string[],
    pattern: string,
    options: {
      case_sensitive: boolean;
      regex: boolean;
      include_line_numbers: boolean;
      context_lines: number;
      max_results: number;
    }
  ): Promise<SearchResult[]> {
    const results: SearchResult[] = [];
    let totalMatches = 0;

    for (const file of files) {
      if (totalMatches >= options.max_results) {
        break;
      }

      try {
        const content = fs.readFileSync(file, 'utf8');
        const matches = this.searchInContent(content, pattern, options);

        if (matches.length > 0) {
          results.push({
            file,
            matches: matches.slice(0, options.max_results - totalMatches)
          });
          totalMatches += matches.length;
        }
      } catch (error) {
        // Skip files that can't be read
        continue;
      }
    }

    return results;
  }

  private searchInContent(
    content: string,
    pattern: string,
    options: {
      case_sensitive: boolean;
      regex: boolean;
      include_line_numbers: boolean;
      context_lines: number;
    }
  ): Match[] {
    const lines = content.split('\n');
    const matches: Match[] = [];

    let searchRegex: RegExp;

    if (options.regex) {
      const flags = options.case_sensitive ? 'g' : 'gi';
      searchRegex = new RegExp(pattern, flags);
    } else {
      const escapedPattern = pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const flags = options.case_sensitive ? 'g' : 'gi';
      searchRegex = new RegExp(escapedPattern, flags);
    }

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const lineMatches = Array.from(line.matchAll(searchRegex));

      if (lineMatches.length > 0) {
        const contextStart = Math.max(0, i - options.context_lines);
        const contextEnd = Math.min(lines.length - 1, i + options.context_lines);

        const contextLines: string[] = [];
        for (let j = contextStart; j <= contextEnd; j++) {
          const prefix = j === i ? '>' : ' ';
          const lineNum = options.include_line_numbers ? `${j + 1}:` : '';
          contextLines.push(`${prefix} ${lineNum} ${lines[j]}`);
        }

        matches.push({
          lineNumber: i + 1,
          line,
          matchCount: lineMatches.length,
          context: contextLines
        });
      }
    }

    return matches;
  }

  private formatResults(results: SearchResult[]): string {
    if (results.length === 0) {
      return 'No matches found.';
    }

    let output = `Found matches in ${results.length} file(s):\n\n`;

    for (const result of results) {
      output += `File: ${result.file}\n`;
      output += `Matches: ${result.matches.length}\n`;
      output += '─'.repeat(50) + '\n';

      for (const match of result.matches) {
        if (match.context.length > 0) {
          output += match.context.join('\n') + '\n';
        } else {
          output += `Line ${match.lineNumber}: ${match.line}\n`;
        }
        output += '\n';
      }

      output += '\n';
    }

    return output;
  }

  // Helper method for finding function definitions
  async findFunctions(filePath: string, language?: string): Promise<ToolResult> {
    const patterns: Record<string, string> = {
      javascript: '(function\\s+\\w+|\\w+\\s*=\\s*function|\\w+\\s*\\([^)]*\\)\\s*=>|class\\s+\\w+)',
      typescript: '(function\\s+\\w+|\\w+\\s*=\\s*function|\\w+\\s*\\([^)]*\\)\\s*=>|class\\s+\\w+|interface\\s+\\w+)',
      python: '(def\\s+\\w+|class\\s+\\w+)',
      java: '(public|private|protected)?\\s*(static)?\\s*\\w+\\s+\\w+\\s*\\(',
      cpp: '(\\w+\\s+\\w+\\s*\\(|class\\s+\\w+)',
      default: '(function|def|class|interface)\\s+\\w+'
    };

    const detectedLang = language || this.detectLanguage(filePath);
    const pattern = patterns[detectedLang] || patterns.default;

    return await this.execute({
      pattern,
      path: filePath,
      regex: true,
      include_line_numbers: true
    });
  }

  private detectLanguage(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    const langMap: Record<string, string> = {
      '.js': 'javascript',
      '.ts': 'typescript',
      '.py': 'python',
      '.java': 'java',
      '.cpp': 'cpp',
      '.c': 'cpp'
    };

    return langMap[ext] || 'default';
  }
}

interface SearchResult {
  file: string;
  matches: Match[];
}

interface Match {
  lineNumber: number;
  line: string;
  matchCount: number;
  context: string[];
}
